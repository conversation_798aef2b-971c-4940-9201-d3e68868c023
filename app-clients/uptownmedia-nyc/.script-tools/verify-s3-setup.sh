#!/bin/bash

echo "🔍 Verificando configuración de S3 y LocalStack..."

# Check if LocalStack is running
echo "📋 Verificando si LocalStack está ejecutándose..."
if docker ps | grep -q localstack; then
    echo "✅ LocalStack está ejecutándose"
else
    echo "❌ LocalStack no está ejecutándose"
    echo "💡 Ejecuta: make run-uptownmedia"
    exit 1
fi

# Check if awslocal is installed
echo "📋 Verificando si awslocal está instalado..."
if command -v awslocal &> /dev/null; then
    echo "✅ awslocal está instalado"
else
    echo "❌ awslocal no está instalado"
    echo "💡 Instala con: pip install awscli-local"
    exit 1
fi

# Check if bucket exists
echo "📋 Verificando si el bucket S3 existe..."
BUCKET_NAME=${AWS_S3_BUCKET_NAME:-"up-bucket"}
if awslocal s3 ls s3://$BUCKET_NAME 2>/dev/null; then
    echo "✅ Bucket '$BUCKET_NAME' existe"
else
    echo "⚠️  Bucket '$BUCKET_NAME' no existe, creándolo..."
    if awslocal s3 mb s3://$BUCKET_NAME; then
        echo "✅ Bucket '$BUCKET_NAME' creado exitosamente"
    else
        echo "❌ Error al crear el bucket '$BUCKET_NAME'"
        exit 1
    fi
fi

# Test bucket access
echo "📋 Probando acceso al bucket..."
TEST_FILE="/tmp/test-upload-$(date +%s).txt"
echo "Test file content" > $TEST_FILE

if awslocal s3 cp $TEST_FILE s3://$BUCKET_NAME/test/ 2>/dev/null; then
    echo "✅ Subida de archivo de prueba exitosa"
    awslocal s3 rm s3://$BUCKET_NAME/test/$(basename $TEST_FILE) 2>/dev/null
    rm $TEST_FILE
else
    echo "❌ Error al subir archivo de prueba"
    rm $TEST_FILE
    exit 1
fi

echo ""
echo "🎉 ¡Configuración de S3 verificada exitosamente!"
echo ""
echo "📝 Información de configuración:"
echo "   - Bucket: $BUCKET_NAME"
echo "   - Endpoint: http://localhost:4566"
echo "   - Región: us-east-1"
echo ""
echo "💡 Ahora puedes probar la subida de imágenes en el editor."
