/** @type {import('next').NextConfig} */
const nextConfig = {
	output: "standalone",
	images: {
		remotePatterns: [
			{
				protocol: "https",
				hostname: "picsum.photos", //TODO: this configuration is temporary, it should be removed in the near future
			},
			{
				protocol: "https",
				hostname: "lh3.googleusercontent.com",
			},
			{
				protocol: "https",
				hostname: "avatars.githubusercontent.com",
			},
			{
				protocol: "http",
				hostname: "localhost",
				port: "4566",
				pathname: "/**",
			},
		],
	},
	eslint: {
		ignoreDuringBuilds: true,
	},
	experimental: {
		serverActions: {
			allowedForwardedHosts: [
				"uptownmedia.nyc",
				"y43e8niwl6.execute-api.us-east-1.amazonaws.com",
			],
			allowedHosts: [
				"uptownmedia.nyc",
				"y43e8niwl6.execute-api.us-east-1.amazonaws.com",
			],
			allowedOrigins: [
				"uptownmedia.nyc",
				"y43e8niwl6.execute-api.us-east-1.amazonaws.com",
			],
		},
	},

	async headers() {
		return [
			{
				source: "/api/:path*",
				headers: [
					{ key: "Access-Control-Allow-Credentials", value: "true" },
					{ key: "Access-Control-Allow-Origin", value: "*" },
					{
						key: "Access-Control-Allow-Methods",
						value: "GET,DELETE,PATCH,POST,PUT",
					},
					{
						key: "Access-Control-Allow-Headers",
						value:
							"X-CSRF-Token, X-Requested-With, Accept, Accept-Version, Content-Length, Content-MD5, Content-Type, Date, X-Api-Version",
					},
				],
			},
		];
	},
};

module.exports = nextConfig;
