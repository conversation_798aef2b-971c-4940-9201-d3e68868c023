import { type NextRequest, NextResponse } from "next/server";
import { uuidv7 } from "uuidv7";

import { S3Service } from "../../external/s3/s3Services";

const s3 = new S3Service();

const MAX_SIZE = 5 * 1024 * 1024; // 5 MB
const ALLOWED_TYPES = ["image/jpeg", "image/png", "image/webp"];

export async function POST(req: NextRequest) {
	const formData = await req.formData();
	const file = formData.get("file") as File;

	if (!file) {
		return NextResponse.json({ error: "File is required" }, { status: 400 });
	}

	if (!ALLOWED_TYPES.includes(file.type)) {
		return NextResponse.json({ error: "Invalid file type" }, { status: 400 });
	}

	if (file.size > MAX_SIZE) {
		return NextResponse.json({ error: "File is too large" }, { status: 400 });
	}

	const arrayBuffer = await file.arrayBuffer();
	const buffer = Buffer.from(arrayBuffer);
	const contentType = file.type;
	const extension = file.name.split(".").pop();
	const key = `develop/uptownmedia-nyc/articles/img/uploads/${uuidv7()}.${extension}`;

	try {
		console.log(`Attempting to upload file: ${key} with content type: ${contentType}`);
		const url = await s3.uploadFile(key, buffer, contentType);
		console.log(`File uploaded successfully: ${url}`);

		return NextResponse.json({
			url,
		});
	} catch (error) {
		console.error("S3 Upload Error:", error);

		// Provide more specific error messages
		let errorMessage = "Failed to upload";
		if (error instanceof Error) {
			errorMessage = error.message;
		}

		return NextResponse.json({
			error: errorMessage,
			details: process.env.NODE_ENV === "development" ? String(error) : undefined
		}, { status: 500 });
	}
}
