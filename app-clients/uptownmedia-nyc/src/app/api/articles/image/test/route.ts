import { type NextRequest, NextResponse } from "next/server";

import { S3Service } from "../../../external/s3/s3Services";

const s3 = new S3Service();

export async function GET(req: NextRequest) {
	try {
		// Test S3 connection by attempting to create a simple test file
		const testKey = `develop/uptownmedia-nyc/articles/img/test/connection-test-${Date.now()}.txt`;
		const testContent = Buffer.from("Test connection", "utf-8");
		
		console.log("Testing S3 connection...");
		console.log("Environment:", process.env.NODE_ENV);
		console.log("AWS Region:", process.env.AWS_REGION || "us-east-1");
		console.log("S3 Bucket:", process.env.AWS_S3_BUCKET_NAME || "up-bucket");
		
		const url = await s3.uploadFile(testKey, testContent, "text/plain");
		
		return NextResponse.json({
			status: "success",
			message: "S3 connection is working",
			testFileUrl: url,
			environment: process.env.NODE_ENV,
			bucket: process.env.AWS_S3_BUCKET_NAME || "up-bucket",
			region: process.env.AWS_REGION || "us-east-1"
		});
	} catch (error) {
		console.error("S3 Connection Test Failed:", error);
		
		return NextResponse.json({
			status: "error",
			message: "S3 connection failed",
			error: error instanceof Error ? error.message : String(error),
			environment: process.env.NODE_ENV,
			bucket: process.env.AWS_S3_BUCKET_NAME || "up-bucket",
			region: process.env.AWS_REGION || "us-east-1",
			details: process.env.NODE_ENV === "development" ? String(error) : undefined
		}, { status: 500 });
	}
}
