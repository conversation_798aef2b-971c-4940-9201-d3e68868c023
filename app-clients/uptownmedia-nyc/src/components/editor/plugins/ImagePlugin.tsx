"use client";

import { useLexicalComposerContext } from "@lexical/react/LexicalComposerContext";
import { $wrapNodeInElement, mergeRegister } from "@lexical/utils";
import {
	$createParagraphNode,
	$insertNodes,
	$isRootOrShadowRoot,
	COMMAND_PRIORITY_EDITOR,
	type Lexical<PERSON>ommand,
	createCommand,
} from "lexical";
import { useEffect } from "react";

import {
	$createImageNode,
	ImageNode,
	type ImagePayload,
} from "../nodes/ImageNode";

export type InsertImagePayload = Readonly<ImagePayload>;

export const INSERT_IMAGE_COMMAND: LexicalCommand<InsertImagePayload> =
	createCommand("INSERT_IMAGE_COMMAND");

export default function ImagePlugin(): JSX.Element | null {
	const [editor] = useLexicalComposerContext();

	useEffect(() => {
		if (!editor.hasNodes([ImageNode])) {
			throw new Error("ImagePlugin: ImageNode not registered on editor");
		}

		return mergeRegister(
			editor.registerCommand(
				INSERT_IMAGE_COMMAND,
				(payload) => {
					console.log("INSERT_IMAGE_COMMAND received with payload:", payload);
					const imageNode = $createImageNode(payload);
					console.log("Image node created:", imageNode);
					$insertNodes([imageNode]);
					console.log("Image node inserted");
					if ($isRootOrShadowRoot(imageNode.getParentOrThrow())) {
						$wrapNodeInElement(imageNode, $createParagraphNode).selectEnd();
						console.log("Image node wrapped in paragraph");
					}
					return true;
				},
				COMMAND_PRIORITY_EDITOR,
			),
		);
	}, [editor]);

	return null;
}

// Utility function to upload image to S3
export async function uploadImageToS3(file: File): Promise<string> {
	// Validate file type on client side
	const allowedTypes = ["image/jpeg", "image/jpg", "image/png", "image/webp"];
	if (!allowedTypes.includes(file.type)) {
		throw new Error("Tipo de archivo no válido. Solo se permiten JPG, PNG y WebP.");
	}

	// Validate file size (5MB max)
	const maxSize = 5 * 1024 * 1024; // 5MB
	if (file.size > maxSize) {
		throw new Error("El archivo es demasiado grande. Máximo 5MB permitido.");
	}

	const formData = new FormData();
	formData.append("file", file);

	try {
		const response = await fetch("/api/articles/image", {
			method: "POST",
			body: formData,
		});

		if (!response.ok) {
			let errorMessage = "Error al subir la imagen";
			try {
				const errorData = await response.json();
				errorMessage = errorData.error || errorMessage;
			} catch {
				// If response is not JSON, use status text
				errorMessage = `Error ${response.status}: ${response.statusText}`;
			}
			throw new Error(errorMessage);
		}

		const data = await response.json();

		if (!data.url) {
			throw new Error("No se recibió la URL de la imagen subida");
		}

		return data.url;
	} catch (error) {
		console.error("Error uploading image:", error);

		if (error instanceof Error) {
			throw error;
		}

		throw new Error("Error de conexión al subir la imagen");
	}
}
