"use client";

import { Button } from "@/components/ui/button/Button";
import { useLexicalComposerContext } from "@lexical/react/LexicalComposerContext";
import { $findMatchingParent, mergeRegister } from "@lexical/utils";
import {
	$getSelection,
	$isRangeSelection,
	$isRootOrShadowRoot,
	CAN_REDO_COMMAND,
	CAN_UNDO_COMMAND,
	FORMAT_ELEMENT_COMMAND,
	FORMAT_TEXT_COMMAND,
	REDO_COMMAND,
	SELECTION_CHANGE_COMMAND,
	UNDO_COMMAND,
} from "lexical";
import { useCallback, useEffect, useRef, useState } from "react";

import { INSERT_IMAGE_COMMAND, uploadImageToS3 } from "./ImagePlugin";

const LowPriority = 1;

function Divider() {
	return <div className="w-px mx-1 bg-gray-300" />;
}

export default function ToolbarPlugin(): JSX.Element {
	const [editor] = useLexicalComposerContext();
	const toolbarRef = useRef(null);
	const [canUndo, setCanUndo] = useState(false);
	const [canRedo, setCanRedo] = useState(false);
	const [isBold, setIsBold] = useState(false);
	const [isItalic, setIsItalic] = useState(false);
	const [isUnderline, setIsUnderline] = useState(false);
	const [isStrikethrough, setIsStrikethrough] = useState(false);

	const [isUploading, setIsUploading] = useState(false);
	const fileInputRef = useRef<HTMLInputElement>(null);

	const $updateToolbar = useCallback(() => {
		const selection = $getSelection();
		if ($isRangeSelection(selection)) {
			const anchorNode = selection.anchor.getNode();
			let element =
				anchorNode.getKey() === "root"
					? anchorNode
					: $findMatchingParent(anchorNode, (e) => {
							const parent = e.getParent();
							return parent !== null && $isRootOrShadowRoot(parent);
						});

			if (element === null) {
				element = anchorNode.getTopLevelElementOrThrow();
			}

			// Update text format
			setIsBold(selection.hasFormat("bold"));
			setIsItalic(selection.hasFormat("italic"));
			setIsUnderline(selection.hasFormat("underline"));
			setIsStrikethrough(selection.hasFormat("strikethrough"));
		}
	}, []);

	useEffect(() => {
		return mergeRegister(
			editor.registerUpdateListener(({ editorState }) => {
				editorState.read(() => {
					$updateToolbar();
				});
			}),
			editor.registerCommand(
				SELECTION_CHANGE_COMMAND,
				(_payload, _newEditor) => {
					$updateToolbar();
					return false;
				},
				LowPriority,
			),
			editor.registerCommand(
				CAN_UNDO_COMMAND,
				(payload) => {
					setCanUndo(payload);
					return false;
				},
				LowPriority,
			),
			editor.registerCommand(
				CAN_REDO_COMMAND,
				(payload) => {
					setCanRedo(payload);
					return false;
				},
				LowPriority,
			),
		);
	}, [editor, $updateToolbar]);

	const handleImageUpload = useCallback(
		async (event: React.ChangeEvent<HTMLInputElement>) => {
			const files = event.target.files;
			if (!files || files.length === 0) return;

			const file = files[0];
			if (!file.type.startsWith("image/")) {
				alert("Please select an image file");
				return;
			}

			setIsUploading(true);
			try {
				const imageUrl = await uploadImageToS3(file);
				editor.dispatchCommand(INSERT_IMAGE_COMMAND, {
					altText: file.name,
					src: imageUrl,
				});
			} catch (error) {
				console.error("Error uploading image:", error);
				alert("Failed to upload image. Please try again.");
			} finally {
				setIsUploading(false);
				// Reset file input
				if (fileInputRef.current) {
					fileInputRef.current.value = "";
				}
			}
		},
		[editor],
	);

	return (
		<div
			className="flex flex-wrap items-center gap-1 p-2 overflow-hidden border-b border-gray-200 rounded-t-lg bg-gray-50"
			ref={toolbarRef}
		>
			<Button
				color="secondary"
				size="sm"
				isDisabled={!canUndo}
				onClick={() => {
					editor.dispatchCommand(UNDO_COMMAND, undefined);
				}}
				aria-label="Undo"
			>
				↶
			</Button>
			<Button
				color="secondary"
				size="sm"
				isDisabled={!canRedo}
				onClick={() => {
					editor.dispatchCommand(REDO_COMMAND, undefined);
				}}
				aria-label="Redo"
			>
				↷
			</Button>

			<Divider />

			<Button
				color={isBold ? "primary" : "secondary"}
				size="sm"
				onClick={() => {
					editor.dispatchCommand(FORMAT_TEXT_COMMAND, "bold");
				}}
				aria-label="Format Bold"
			>
				<strong>B</strong>
			</Button>
			<Button
				color={isItalic ? "primary" : "secondary"}
				size="sm"
				onClick={() => {
					editor.dispatchCommand(FORMAT_TEXT_COMMAND, "italic");
				}}
				aria-label="Format Italics"
			>
				<em>I</em>
			</Button>
			<Button
				color={isUnderline ? "primary" : "secondary"}
				size="sm"
				onClick={() => {
					editor.dispatchCommand(FORMAT_TEXT_COMMAND, "underline");
				}}
				aria-label="Format Underline"
			>
				<u>U</u>
			</Button>
			<Button
				color={isStrikethrough ? "primary" : "secondary"}
				size="sm"
				onClick={() => {
					editor.dispatchCommand(FORMAT_TEXT_COMMAND, "strikethrough");
				}}
				aria-label="Format Strikethrough"
			>
				<s>S</s>
			</Button>

			<Divider />

			<Button
				color="secondary"
				size="sm"
				onClick={() => {
					editor.dispatchCommand(FORMAT_ELEMENT_COMMAND, "left");
				}}
				aria-label="Left Align"
			>
				⬅
			</Button>
			<Button
				color="secondary"
				size="sm"
				onClick={() => {
					editor.dispatchCommand(FORMAT_ELEMENT_COMMAND, "center");
				}}
				aria-label="Center Align"
			>
				⬌
			</Button>
			<Button
				color="secondary"
				size="sm"
				onClick={() => {
					editor.dispatchCommand(FORMAT_ELEMENT_COMMAND, "right");
				}}
				aria-label="Right Align"
			>
				➡
			</Button>

			<Divider />

			<Button
				color="secondary"
				size="sm"
				isDisabled={isUploading}
				onClick={() => fileInputRef.current?.click()}
				aria-label="Insert Image"
			>
				{isUploading ? "Uploading..." : "🖼️"}
			</Button>
			<input
				type="file"
				ref={fileInputRef}
				onChange={handleImageUpload}
				accept="image/*"
				style={{ display: "none" }}
			/>
		</div>
	);
}
