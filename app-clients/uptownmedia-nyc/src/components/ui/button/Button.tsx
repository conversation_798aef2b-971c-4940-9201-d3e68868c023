'use client';
import {extendVariants, Button as BaseButton} from "@heroui/react";

export const Button = extendVariants(BaseButton, {
  variants: {
    color: {
      primary: "text-white bg-primary-500 hover:bg-primary-600",
      secondary: "text-neutral-500 bg-white hover:bg-neutral-700 hover:text-white border border-neutral-500",
    },
    isDisabled: {
        true: ""
      },
    size: {
      xs: "p-1 h-fit rounded-[0.375rem] text-xs font-medium flex-shrink-0 font-poppins font-semibold min-w-8 min-h-8",
      sm: "p-2 h-fit rounded-[0.438rem] text-xs font-medium flex-shrink-0 font-poppins font-semibold min-w-9 min-h-9",
      lg: "p-[0.625rem] h-fit rounded-[0.563rem] text-sm font-medium flex-shrink-0 font-poppins font-semibold",
    },
  },
  defaultVariants: {
    color: "secondary",
    size: "lg",
  },
  compoundVariants: [
    {
        isDisabled: true,
        color: "primary",
        class: "bg-primary-100 border-none text-primary-400 opacity-50 cursor-not-allowed"
    },
    {
        isDisabled: true,
        color: "secondary",
        class: "bg-neutral-100 border-none text-neutral-500 opacity-50 cursor-not-allowed"
    },
  ],
});